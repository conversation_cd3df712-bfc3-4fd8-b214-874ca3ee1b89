"""
TeleConnect Customer Support RAG Bot - Streamlit Interface
Main application file for the customer support chatbot
"""

import streamlit as st
from datetime import datetime
from typing import Dict
import logging

# Import our RAG pipeline and voice features
from rag_pipeline import TelecomRAGPipeline
from streamlit_voice import get_voice_input, test_microphone_access
from voice_output import create_voice_output_button, play_response_voice, VoiceOutputHandler
from enhanced_voice import create_voice_conversation_widget, EnhancedVoiceHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="TeleConnect Customer Support",
    page_icon="📞",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79, #2e86ab);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }

    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #2e86ab;
    }

    .user-message {
        background-color: #f0f8ff;
        border-left-color: #1f4e79;
    }

    .assistant-message {
        background-color: #f8f9fa;
        border-left-color: #2e86ab;
    }

    .sidebar-info {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 5px;
        margin: 0.5rem 0;
    }

    .followup-questions {
        background-color: #e8f4f8;
        padding: 1rem;
        border-radius: 5px;
        margin-top: 1rem;
    }

    .intent-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 0.25rem;
    }

    .intent-technical { background-color: #ffeaa7; color: #2d3436; }
    .intent-billing { background-color: #fab1a0; color: #2d3436; }
    .intent-general { background-color: #a8e6cf; color: #2d3436; }
    .intent-complaint { background-color: #ff7675; color: white; }
    .intent-emergency { background-color: #e17055; color: white; }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def initialize_rag_pipeline():
    """Initialize the RAG pipeline (cached for performance)"""
    try:
        with st.spinner("Initializing RAG pipeline..."):
            rag = TelecomRAGPipeline()
            st.success("RAG pipeline initialized successfully!")
            return rag
    except Exception as e:
        st.error(f"Failed to initialize RAG pipeline: {str(e)}")
        st.error("Please check your GOOGLE_API_KEY in the .env file")
        st.exception(e)
        return None

def display_message(role: str, content: str, timestamp: str = None, metadata: Dict = None):
    """Display a chat message with proper styling"""
    if timestamp is None:
        timestamp = datetime.now().strftime("%H:%M:%S")

    css_class = "user-message" if role == "user" else "assistant-message"
    icon = "👤" if role == "user" else "🤖"

    # Create message layout with voice button for assistant messages
    if role == "assistant":
        col1, col2 = st.columns([10, 1])
        with col1:
            st.markdown(f"""
            <div class="chat-message {css_class}">
                <strong>{icon} {role.title()}</strong> <small>({timestamp})</small><br>
                {content}
            </div>
            """, unsafe_allow_html=True)

        with col2:
            # Voice output button for assistant messages
            if st.session_state.get('voice_output_enabled', False):
                if st.button("🔊", key=f"voice_{timestamp}_{hash(content)}", help="Listen to response"):
                    with st.spinner("🔊 Speaking..."):
                        if 'voice_output_handler' not in st.session_state:
                            st.session_state.voice_output_handler = VoiceOutputHandler()

                        handler = st.session_state.voice_output_handler

                        # Debug information
                        if not handler.is_tts_available():
                            st.error("❌ TTS engine not available")
                        elif not content.strip():
                            st.error("❌ No content to speak")
                        else:
                            success = handler.speak_text(content)
                            if success:
                                st.success("✅ Voice played", icon="🔊")
                            else:
                                st.error("❌ Voice failed - Check console for details")
    else:
        # Regular message display for user messages
        st.markdown(f"""
        <div class="chat-message {css_class}">
            <strong>{icon} {role.title()}</strong> <small>({timestamp})</small><br>
            {content}
        </div>
        """, unsafe_allow_html=True)

    # Display metadata for assistant messages
    if role == "assistant" and metadata:
        intent = metadata.get("intent", {})
        if intent:
            category = intent.get("category", "general")
            urgency = intent.get("urgency", "medium")

            intent_class = f"intent-{category.replace('_', '-')}"
            st.markdown(f"""
            <div style="margin-top: 0.5rem;">
                <span class="intent-badge {intent_class}">
                    {category.replace('_', ' ').title()} - {urgency.title()} Priority
                </span>
            </div>
            """, unsafe_allow_html=True)

def display_followup_questions(questions: list, session_id: str):
    """Display follow-up questions as clickable buttons"""
    if questions:
        st.markdown('<div class="followup-questions">', unsafe_allow_html=True)
        st.markdown("**💡 Suggested follow-up questions:**")

        cols = st.columns(len(questions))
        for i, question in enumerate(questions):
            with cols[i]:
                if st.button(question, key=f"followup_{session_id}_{i}"):
                    st.session_state.followup_clicked = question

        st.markdown('</div>', unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>📞 TeleConnect Customer Support</h1>
        <p>AI-Powered Customer Support Assistant</p>
    </div>
    """, unsafe_allow_html=True)

    # Initialize RAG pipeline
    rag = initialize_rag_pipeline()
    if not rag:
        st.error("❌ RAG pipeline failed to initialize. Please check the logs above.")
        st.info("💡 Make sure your GOOGLE_API_KEY is set correctly in the .env file")
        st.stop()

    # Initialize session state
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "session_id" not in st.session_state:
        st.session_state.session_id = None
    if "customer_info" not in st.session_state:
        st.session_state.customer_info = {}

    # Sidebar
    with st.sidebar:
        st.header("🔧 System Information")

        # System stats
        with st.expander("System Status", expanded=True):
            try:
                stats = rag.get_system_stats()

                # API Status
                gemini_embeddings_status = "✅" if stats.get("api_status", {}).get("gemini_embeddings") else "❌"
                gemini_llm_status = "✅" if stats.get("api_status", {}).get("gemini_llm") else "❌"

                st.markdown(f"""
                <div class="sidebar-info">
                    <strong>API Status:</strong><br>
                    Gemini Embeddings: {gemini_embeddings_status}<br>
                    Gemini LLM: {gemini_llm_status}<br><br>

                    <strong>Knowledge Base:</strong><br>
                    Documents: {stats.get('vector_database', {}).get('total_documents', 0)}<br><br>

                    <strong>Active Sessions:</strong><br>
                    {stats.get('conversation_memory', {}).get('active_sessions', 0)}
                </div>
                """, unsafe_allow_html=True)

            except Exception as e:
                st.error(f"Error loading system stats: {str(e)}")

        # Customer Information
        st.header("👤 Customer Information")
        with st.expander("Update Customer Info"):
            account_number = st.text_input("Account Number",
                                         value=st.session_state.customer_info.get("account_number", ""))
            plan_type = st.selectbox("Plan Type",
                                   ["", "Basic Mobile", "Standard Mobile", "Premium Mobile",
                                    "Basic Internet", "Standard Internet", "Premium Internet"],
                                   index=0)
            service_area = st.text_input("Service Area",
                                       value=st.session_state.customer_info.get("service_area", ""))

            if st.button("Update Info"):
                customer_info = {}
                if account_number:
                    customer_info["account_number"] = account_number
                if plan_type:
                    customer_info["plan_type"] = plan_type
                if service_area:
                    customer_info["service_area"] = service_area

                if customer_info and st.session_state.session_id:
                    rag.update_customer_info(st.session_state.session_id, customer_info)
                    st.session_state.customer_info.update(customer_info)
                    st.success("Customer information updated!")

        # Voice Features Status
        st.header("🎤🔊 Voice Features")

        # Voice Input Status
        mic_status = test_microphone_access()
        mic_icon = "✅" if mic_status else "❌"
        mic_text = "Available" if mic_status else "Not Available"

        # Voice Output Status
        if 'voice_output_handler' not in st.session_state:
            st.session_state.voice_output_handler = VoiceOutputHandler()

        tts_status = st.session_state.voice_output_handler.is_tts_available()
        tts_icon = "✅" if tts_status else "❌"
        tts_text = "Available" if tts_status else "Not Available"

        st.markdown(f"""
        <div class="sidebar-info">
            <strong>🎤 Voice Input:</strong> {mic_icon} {mic_text}<br>
            <strong>🔊 Voice Output:</strong> {tts_icon} {tts_text}<br><br>

            <strong>Voice Input:</strong><br>
            1. Click 🎤 button in chat area<br>
            2. Allow microphone access<br>
            3. Speak clearly (8 seconds max)<br>
            4. Speech converts to text<br><br>

            <strong>Voice Output:</strong><br>
            1. Enable in settings below<br>
            2. Bot responses will be spoken<br>
            3. Click 🔊 button to replay<br><br>

            <strong>Tips:</strong><br>
            • Speak at normal pace<br>
            • Minimize background noise<br>
            • Use complete sentences
        </div>
        """, unsafe_allow_html=True)

        # Voice Output Settings
        with st.expander("🔊 Voice Output Settings"):
            voice_enabled = st.checkbox("Enable Voice Responses",
                                       value=st.session_state.get('voice_output_enabled', False),
                                       help="Bot will speak responses aloud")
            st.session_state.voice_output_enabled = voice_enabled

            if voice_enabled:
                auto_play = st.checkbox("Auto-play Responses",
                                       value=st.session_state.get('voice_auto_play', True),
                                       help="Automatically play voice when response is generated")
                st.session_state.voice_auto_play = auto_play

                # Voice settings
                rate = st.slider("Speech Rate", 100, 300, 180, help="Words per minute")
                volume = st.slider("Volume", 0.0, 1.0, 0.9, 0.1)

                # Apply settings
                if st.button("Apply Voice Settings", key="apply_voice_settings"):
                    st.session_state.voice_output_handler.set_voice_settings(rate=rate, volume=volume)
                    st.success("Voice settings updated!")

                # Test voice button
                if st.button("🔊 Test Voice", key="test_voice_btn", help="Test voice output"):
                    with st.spinner("🔊 Testing voice..."):
                        if 'voice_output_handler' not in st.session_state:
                            st.session_state.voice_output_handler = VoiceOutputHandler()

                        handler = st.session_state.voice_output_handler
                        test_text = "Hello! This is a test of the voice output system."

                        if not handler.is_tts_available():
                            st.error("❌ TTS engine not available")
                        else:
                            success = handler.speak_text(test_text)
                            if success:
                                st.success("✅ Voice test successful!")
                            else:
                                st.error("❌ Voice test failed - Check console for details")

        # Session Management
        st.header("💬 Session Management")
        if st.button("Start New Session"):
            st.session_state.messages = []
            st.session_state.session_id = None
            st.session_state.customer_info = {}
            st.rerun()

        if st.session_state.session_id:
            st.info(f"Session ID: {st.session_state.session_id[:8]}...")

            # Session info
            try:
                session_info = rag.get_session_info(st.session_state.session_id)
                st.markdown(f"""
                <div class="sidebar-info">
                    <strong>Session Summary:</strong><br>
                    Messages: {session_info.get('message_count', 0)}<br>
                    {session_info.get('conversation_summary', 'No summary available')}
                </div>
                """, unsafe_allow_html=True)
            except Exception as e:
                st.error(f"Error loading session info: {str(e)}")

    # Main chat interface
    st.header("💬 Chat with Support Assistant")

    # Display chat messages
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.messages:
            display_message(
                role=message["role"],
                content=message["content"],
                timestamp=message.get("timestamp"),
                metadata=message.get("metadata")
            )

    # Handle follow-up question clicks
    if hasattr(st.session_state, 'followup_clicked'):
        user_input = st.session_state.followup_clicked
        delattr(st.session_state, 'followup_clicked')
    else:
        # Voice input section
        st.markdown("---")
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            # Chat input
            user_input = st.chat_input("Type your question here...")

        with col2:
            # Voice input button
            voice_text = get_voice_input()
            if voice_text:
                user_input = voice_text

        with col3:
            # Enhanced voice conversation button
            conversation_text = create_voice_conversation_widget()
            if conversation_text:
                user_input = conversation_text

    # Process user input
    if user_input:
        # Add user message to session state
        user_message = {
            "role": "user",
            "content": user_input,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        st.session_state.messages.append(user_message)

        # Display user message
        with chat_container:
            display_message(**user_message)

        # Process query through RAG pipeline
        with st.spinner("🤔 Thinking..."):
            try:
                result = rag.process_query(
                    query=user_input,
                    session_id=st.session_state.session_id,
                    user_id="streamlit_user"
                )

                # Validate that result is a dictionary
                if not isinstance(result, dict):
                    logger.error(f"RAG pipeline returned unexpected type: {type(result)}")
                    # Create a fallback response
                    result = {
                        "response": "I'm experiencing technical difficulties. Please contact our customer service team at 1-800-TELECON for immediate assistance.",
                        "session_id": st.session_state.session_id,
                        "intent": {"category": "error", "urgency": "high", "requires_human": True},
                        "context_sources": [],
                        "followup_questions": ["Would you like me to connect you with a human agent?"],
                        "conversation_summary": "System error occurred"
                    }

                # Update session ID if new
                if not st.session_state.session_id and "session_id" in result:
                    st.session_state.session_id = result["session_id"]

                # Add assistant response to session state
                assistant_message = {
                    "role": "assistant",
                    "content": result.get("response", "I'm sorry, I couldn't process your request."),
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                    "metadata": {
                        "intent": result.get("intent", {}),
                        "context_sources": result.get("context_sources", [])
                    }
                }
                st.session_state.messages.append(assistant_message)

                # Display assistant response
                with chat_container:
                    display_message(**assistant_message)

                # Auto-play voice response if enabled
                if (st.session_state.get('voice_output_enabled', False) and
                    st.session_state.get('voice_auto_play', True)):

                    if 'voice_output_handler' not in st.session_state:
                        st.session_state.voice_output_handler = VoiceOutputHandler()

                    # Play voice synchronously to ensure proper audio output
                    with st.spinner("🔊 Speaking..."):
                        success = st.session_state.voice_output_handler.speak_text(result["response"])
                        if success:
                            st.success("✅ Voice played automatically", icon="🔊")
                        else:
                            st.error("❌ Auto-play voice failed")

                # Display follow-up questions
                followup_questions = result.get("followup_questions", [])
                if followup_questions:
                    display_followup_questions(followup_questions, st.session_state.session_id)

                # Show context sources in expander
                context_sources = result.get("context_sources", [])
                if context_sources:
                    with st.expander("📚 Information Sources"):
                        for source in context_sources:
                            if isinstance(source, dict):
                                relevance = source.get("relevance_score", 0)
                                section = source.get("section", "Unknown")
                                st.markdown(f"• **{section}** (Relevance: {relevance:.2f})")
                            else:
                                st.markdown(f"• **{source}**")

            except Exception as e:
                st.error(f"Error processing query: {str(e)}")
                logger.error(f"Error in main chat processing: {str(e)}")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.8rem;">
        TeleConnect Customer Support RAG Bot | Powered by Gemini AI (Embeddings & LLM)
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
