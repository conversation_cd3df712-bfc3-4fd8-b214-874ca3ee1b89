#!/usr/bin/env python3
"""
Simple Streamlit app to test voice functionality
"""

import streamlit as st
import logging
from voice_output import VoiceOutputHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

st.title("🔊 Voice Output Test")

# Initialize voice handler in session state
if 'voice_handler' not in st.session_state:
    st.session_state.voice_handler = VoiceOutputHandler()

handler = st.session_state.voice_handler

# Display TTS status
st.subheader("TTS Engine Status")
if handler.is_tts_available():
    st.success("✅ TTS Engine Available")
    
    # Get available voices
    voices = handler.get_available_voices()
    st.info(f"Available voices: {len(voices)}")
    for voice in voices:
        st.write(f"- {voice['name']}")
else:
    st.error("❌ TTS Engine Not Available")

# Test voice output
st.subheader("Voice Test")

test_text = st.text_area("Text to speak:", value="Hello! This is a test of the voice output system.")

col1, col2 = st.columns(2)

with col1:
    if st.button("🔊 Test Voice (Sync)", help="Test synchronous voice output"):
        if not handler.is_tts_available():
            st.error("❌ TTS not available")
        elif not test_text.strip():
            st.error("❌ Please enter some text")
        else:
            with st.spinner("🔊 Speaking..."):
                try:
                    success = handler.speak_text(test_text)
                    if success:
                        st.success("✅ Voice output successful!")
                    else:
                        st.error("❌ Voice output failed")
                except Exception as e:
                    st.error(f"❌ Exception: {e}")

with col2:
    if st.button("🔊 Test Voice (Async)", help="Test asynchronous voice output"):
        if not handler.is_tts_available():
            st.error("❌ TTS not available")
        elif not test_text.strip():
            st.error("❌ Please enter some text")
        else:
            try:
                thread = handler.speak_text_async(test_text)
                st.info("🔊 Voice playing in background...")
            except Exception as e:
                st.error(f"❌ Exception: {e}")

# Voice settings
st.subheader("Voice Settings")

if handler.is_tts_available():
    rate = st.slider("Speech Rate (WPM)", 100, 300, 180)
    volume = st.slider("Volume", 0.0, 1.0, 0.9, 0.1)
    
    if st.button("Apply Settings"):
        try:
            handler.set_voice_settings(rate=rate, volume=volume)
            st.success("Settings applied!")
        except Exception as e:
            st.error(f"❌ Failed to apply settings: {e}")

# Debug information
st.subheader("Debug Information")
st.write(f"Handler initialized: {handler is not None}")
st.write(f"TTS engine: {handler.tts_engine is not None if handler else 'No handler'}")
st.write(f"Is speaking: {handler.is_speaking if handler else 'No handler'}")
